
from anthropic import Anthropic

client = Anthropic(
    base_url="https://anthropic.claude-plus.top",
    api_key="sk-I5tdkip29l11propHnNBzVWUAT8pdqEcBd6PsnDlX79EmMC4"
)

file_object = client.beta.files.upload(
    file=open("data.csv", "rb"),
)

response = client.beta.messages.create(
    model="claude-opus-4-20250514",
    betas=["code-execution-2025-05-22", "files-api-2025-04-14"],
    max_tokens=4096,
    messages=[{
        "role": "user",
        "content": [
            {"type": "text", "text": "这个csv中成绩最高的是谁"},
            {"type": "container_upload", "file_id": file_object.id}
        ]
    }],
    tools=[{
        "type": "code_execution_20250522",
        "name": "code_execution"
    }]
)

print(response)