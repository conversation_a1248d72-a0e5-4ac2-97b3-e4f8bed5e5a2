
from anthropic import Anthropic
import os

# 检查文件是否存在
csv_file = "./data.csv"
if not os.path.exists(csv_file):
    print(f"错误: 文件 {csv_file} 不存在")
    exit(1)

try:
    # 创建客户端
    client = Anthropic(
        base_url="https://anthropic.claude-plus.top",
        api_key="sk-I5tdkip29l11propHnNBzVWUAT8pdqEcBd6PsnDlX79EmMC4"
    )

    print("正在上传文件...")
    # 上传文件 - 使用with语句确保文件正确关闭
    with open(csv_file, "rb") as f:
        file_object = client.beta.files.upload(file=f)

    print(f"文件上传成功，文件ID: {file_object.id}")

    print("正在发送消息...")
    # 发送消息
    response = client.beta.messages.create(
        model="claude-opus-4-20250514",
        betas=["code-execution-2025-05-22", "files-api-2025-04-14"],
        max_tokens=4096,
        messages=[{
            "role": "user",
            "content": [
                {"type": "text", "text": "这个csv中成绩最高的是谁"},
                {"type": "container_upload", "file_id": file_object.id}
            ]
        }],
        tools=[{
            "type": "code_execution_20250522",
            "name": "code_execution"
        }]
    )

    print("响应:")
    print(response)

except Exception as e:
    print(f"发生错误: {type(e).__name__}: {e}")
    print("\n可能的解决方案:")
    print("1. 检查API密钥是否有效")
    print("2. 检查网络连接")
    print("3. 检查API服务是否可用")
    print("4. 确认文件格式是否正确")
    print("5. 尝试使用官方Anthropic API端点")